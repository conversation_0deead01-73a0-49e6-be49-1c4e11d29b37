package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.entity.User;
import com.zte.uedm.digitalenergy.domain.permission.entity.UserRoleAssignment;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import com.zte.uedm.digitalenergy.infrastructure.permission.converter.PermissionConverter;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserRoleMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserRolePO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户仓储实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public class UserRepositoryImpl implements UserRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(UserRepositoryImpl.class);
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Override
    @Transactional
    public User save(User user) {
        logger.info("Saving user: {}", user.getUserCode());
        
        UserPO userPO = PermissionConverter.INSTANCE.toUserPO(user);
        
        if (user.getId() == null) {
            // 新增用户
            userPO.setCreateTime(LocalDateTime.now());
            userMapper.insert(userPO);
            user.setId(userPO.getId());
        } else {
            // 更新用户
            userPO.setUpdateTime(LocalDateTime.now());
            userMapper.updateById(userPO);
        }
        
        // 保存用户角色分配
        saveUserRoleAssignments(user.getId(), user.getRoleAssignments());
        
        logger.info("User saved successfully with id: {}", user.getId());
        return user;
    }
    
    @Override
    public Optional<User> findById(Long id) {
        logger.debug("Finding user by id: {}", id);
        
        UserPO userPO = userMapper.selectById(id);
        if (userPO == null) {
            return Optional.empty();
        }
        
        User user = PermissionConverter.INSTANCE.toUser(userPO);
        
        // 加载用户角色分配
        List<Long> roleIds = userRoleMapper.selectRoleIdsByUserId(id);
        for (Long roleId : roleIds) {
            user.assignRole(roleId);
        }
        
        return Optional.of(user);
    }
    
    @Override
    public Optional<User> findByUserCode(String userCode) {
        logger.debug("Finding user by code: {}", userCode);
        
        UserPO userPO = userMapper.selectByUserCode(userCode);
        if (userPO == null) {
            return Optional.empty();
        }
        
        User user = PermissionConverter.INSTANCE.toUser(userPO);
        
        // 加载用户角色分配
        List<Long> roleIds = userRoleMapper.selectRoleIdsByUserId(user.getId());
        for (Long roleId : roleIds) {
            user.assignRole(roleId);
        }
        
        return Optional.of(user);
    }
    
    @Override
    public Optional<User> findByUsername(String username) {
        logger.debug("Finding user by username: {}", username);
        
        UserPO userPO = userMapper.selectByUsername(username);
        if (userPO == null) {
            return Optional.empty();
        }
        
        User user = PermissionConverter.INSTANCE.toUser(userPO);
        
        // 加载用户角色分配
        List<Long> roleIds = userRoleMapper.selectRoleIdsByUserId(user.getId());
        for (Long roleId : roleIds) {
            user.assignRole(roleId);
        }
        
        return Optional.of(user);
    }
    
    @Override
    public List<User> findAll() {
        logger.debug("Finding all users");
        
        List<UserPO> userPOs = userMapper.selectList(null);
        return PermissionConverter.INSTANCE.toUserList(userPOs);
    }
    
    @Override
    public List<User> findByPage(int pageNum, int pageSize, String username, String userCode, String organization) {
        logger.debug("Finding users by page: pageNum={}, pageSize={}, username={}, userCode={}, organization={}", 
                    pageNum, pageSize, username, userCode, organization);
        
        int offset = (pageNum - 1) * pageSize;
        
        List<UserPO> userPOs = userMapper.selectByPage(offset, pageSize, username, userCode, organization);
        return PermissionConverter.INSTANCE.toUserList(userPOs);
    }
    
    @Override
    public long countUsers(String username, String userCode, String organization) {
        logger.debug("Counting users: username={}, userCode={}, organization={}", username, userCode, organization);
        
        return userMapper.countUsers(username, userCode, organization);
    }
    
    @Override
    @Transactional
    public void delete(User user) {
        logger.info("Deleting user: {}", user.getUserCode());
        
        Long userId = user.getId();
        
        // 删除用户角色分配
        userRoleMapper.deleteByUserId(userId);
        
        // 删除用户
        userMapper.deleteById(userId);
        
        logger.info("User deleted successfully: {}", user.getUserCode());
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        logger.info("Deleting user by id: {}", id);
        
        // 删除用户角色分配
        userRoleMapper.deleteByUserId(id);
        
        // 删除用户
        userMapper.deleteById(id);
        
        logger.info("User deleted successfully with id: {}", id);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<Long> ids) {
        logger.info("Deleting users by ids: {}", ids);
        
        for (Long id : ids) {
            deleteById(id);
        }
        
        logger.info("Users deleted successfully, count: {}", ids.size());
    }
    
    @Override
    public boolean existsByUserCode(String userCode) {
        UserPO userPO = userMapper.selectByUserCode(userCode);
        return userPO != null;
    }
    
    @Override
    public boolean existsByUsername(String username) {
        UserPO userPO = userMapper.selectByUsername(username);
        return userPO != null;
    }
    
    @Override
    public boolean existsByUserCodeAndIdNot(String userCode, Long excludeId) {
        return userMapper.countByUserCodeAndIdNot(userCode, excludeId) > 0;
    }
    
    @Override
    public List<User> findByRoleId(Long roleId) {
        logger.debug("Finding users by role id: {}", roleId);
        
        List<UserPO> userPOs = userMapper.selectByRoleId(roleId);
        return PermissionConverter.INSTANCE.toUserList(userPOs);
    }
    
    @Override
    public List<User> findByUserGroupId(Long userGroupId) {
        logger.debug("Finding users by user group id: {}", userGroupId);
        
        List<UserPO> userPOs = userMapper.selectByUserGroupId(userGroupId);
        return PermissionConverter.INSTANCE.toUserList(userPOs);
    }
    
    @Override
    public List<Long> findAllRoleIdsByUserId(Long userId) {
        logger.debug("Finding all role ids by user id: {}", userId);
        
        return userMapper.selectAllRoleIdsByUserId(userId);
    }
    
    @Override
    public List<Long> findDirectRoleIdsByUserId(Long userId) {
        logger.debug("Finding direct role ids by user id: {}", userId);
        
        return userMapper.selectDirectRoleIdsByUserId(userId);
    }
    
    @Override
    public List<Long> findGroupRoleIdsByUserId(Long userId) {
        logger.debug("Finding group role ids by user id: {}", userId);
        
        return userMapper.selectGroupRoleIdsByUserId(userId);
    }
    
    @Override
    public List<Long> findUserGroupIdsByUserId(Long userId) {
        logger.debug("Finding user group ids by user id: {}", userId);
        
        return userMapper.selectUserGroupIdsByUserId(userId);
    }
    
    /**
     * 保存用户角色分配
     * 
     * @param userId 用户ID
     * @param roleAssignments 角色分配列表
     */
    private void saveUserRoleAssignments(Long userId, List<UserRoleAssignment> roleAssignments) {
        logger.debug("Saving user role assignments for user: {}", userId);
        
        // 删除现有分配
        userRoleMapper.deleteByUserId(userId);
        
        // 插入新分配
        if (roleAssignments != null && !roleAssignments.isEmpty()) {
            List<UserRolePO> userRolePOs = roleAssignments.stream()
                    .map(assignment -> {
                        UserRolePO po = new UserRolePO();
                        po.setUserId(userId);
                        po.setRoleId(assignment.getRoleId());
                        po.setCreateTime(LocalDateTime.now());
                        return po;
                    })
                    .collect(Collectors.toList());
            
            userRoleMapper.batchInsert(userRolePOs);
        }
        
        logger.debug("User role assignments saved, count: {}", 
                    roleAssignments != null ? roleAssignments.size() : 0);
    }
}
