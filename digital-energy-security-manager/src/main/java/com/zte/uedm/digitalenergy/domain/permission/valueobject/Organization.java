package com.zte.uedm.digitalenergy.domain.permission.valueobject;

import java.util.Objects;

/**
 * 组织机构值对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class Organization {
    
    /**
     * 组织机构名称
     */
    private final String name;
    
    /**
     * 组织机构编码
     */
    private final String code;
    
    /**
     * 组织机构层级路径
     */
    private final String path;
    
    public Organization(String name, String code, String path) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Organization name cannot be null or empty");
        }
        this.name = name.trim();
        this.code = code;
        this.path = path;
    }
    
    public String getName() {
        return name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getPath() {
        return path;
    }
    
    /**
     * 获取组织机构显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return name;
    }
    
    /**
     * 判断是否为有效的组织机构
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Organization that = (Organization) o;
        return Objects.equals(name, that.name) &&
               Objects.equals(code, that.code) &&
               Objects.equals(path, that.path);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, code, path);
    }
    
    @Override
    public String toString() {
        return "Organization{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", path='" + path + '\'' +
                '}';
    }
}
