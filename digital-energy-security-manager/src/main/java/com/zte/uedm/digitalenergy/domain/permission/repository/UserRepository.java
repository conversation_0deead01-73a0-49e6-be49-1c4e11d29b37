package com.zte.uedm.digitalenergy.domain.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.entity.User;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓储接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface UserRepository {
    
    /**
     * 保存用户
     * 
     * @param user 用户实体
     * @return 保存后的用户实体
     */
    User save(User user);
    
    /**
     * 根据ID查找用户
     * 
     * @param id 用户ID
     * @return 用户实体
     */
    Optional<User> findById(Long id);
    
    /**
     * 根据用户工号查找用户
     * 
     * @param userCode 用户工号
     * @return 用户实体
     */
    Optional<User> findByUserCode(String userCode);
    
    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户实体
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 查找所有用户
     * 
     * @return 用户列表
     */
    List<User> findAll();
    
    /**
     * 分页查询用户列表
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param username 用户名（可选）
     * @param userCode 用户工号（可选）
     * @param organization 组织机构（可选）
     * @return 用户列表
     */
    List<User> findByPage(int pageNum, int pageSize, String username, String userCode, String organization);
    
    /**
     * 统计用户总数
     * 
     * @param username 用户名（可选）
     * @param userCode 用户工号（可选）
     * @param organization 组织机构（可选）
     * @return 用户总数
     */
    long countUsers(String username, String userCode, String organization);
    
    /**
     * 删除用户
     * 
     * @param user 用户实体
     */
    void delete(User user);
    
    /**
     * 根据ID删除用户
     * 
     * @param id 用户ID
     */
    void deleteById(Long id);
    
    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     */
    void deleteByIds(List<Long> ids);
    
    /**
     * 检查用户工号是否存在
     * 
     * @param userCode 用户工号
     * @return true-存在，false-不存在
     */
    boolean existsByUserCode(String userCode);
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return true-存在，false-不存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查用户工号是否存在（排除指定ID）
     * 
     * @param userCode 用户工号
     * @param excludeId 排除的用户ID
     * @return true-存在，false-不存在
     */
    boolean existsByUserCodeAndIdNot(String userCode, Long excludeId);
    
    /**
     * 根据角色ID查找用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<User> findByRoleId(Long roleId);
    
    /**
     * 根据用户组ID查找用户列表
     * 
     * @param userGroupId 用户组ID
     * @return 用户列表
     */
    List<User> findByUserGroupId(Long userGroupId);
    
    /**
     * 查找用户的所有角色ID（包括直接分配和用户组继承）
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> findAllRoleIdsByUserId(Long userId);
    
    /**
     * 查找用户的直接分配角色ID
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> findDirectRoleIdsByUserId(Long userId);
    
    /**
     * 查找用户通过用户组继承的角色ID
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> findGroupRoleIdsByUserId(Long userId);
    
    /**
     * 查找用户所属的用户组ID列表
     * 
     * @param userId 用户ID
     * @return 用户组ID列表
     */
    List<Long> findUserGroupIdsByUserId(Long userId);
}
