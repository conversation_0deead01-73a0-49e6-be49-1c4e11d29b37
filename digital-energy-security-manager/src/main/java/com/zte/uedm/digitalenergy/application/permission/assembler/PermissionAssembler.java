package com.zte.uedm.digitalenergy.application.permission.assembler;

import com.zte.uedm.digitalenergy.application.permission.dto.*;
import com.zte.uedm.digitalenergy.domain.permission.entity.*;
import com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainService;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限管理组装器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
public class PermissionAssembler {
    
    @Autowired
    private PermissionDomainService permissionDomainService;
    
    // ========== Role 转换 ==========
    
    /**
     * Role实体转RoleDTO
     * 
     * @param role 角色实体
     * @return 角色DTO
     */
    public RoleDTO toRoleDTO(Role role) {
        if (role == null) {
            return null;
        }
        
        RoleDTO dto = new RoleDTO();
        dto.setId(role.getId());
        dto.setRoleName(role.getRoleName());
        dto.setRoleCode(role.getRoleCode());
        dto.setRoleDescription(role.getRoleDescription());
        dto.setRoleType(role.getRoleType() != null ? role.getRoleType().getCode() : null);
        dto.setIsDefault(role.getIsDefault());
        dto.setStatus(role.getStatus() != null ? role.getStatus().getCode() : null);
        dto.setCreateTime(role.getCreateTime());
        dto.setCreateBy(role.getCreateBy());
        dto.setUpdateTime(role.getUpdateTime());
        dto.setUpdateBy(role.getUpdateBy());
        
        // 设置菜单权限ID列表
        if (role.getMenuPermissions() != null) {
            List<Long> menuIds = role.getMenuPermissions().stream()
                    .map(RoleMenuPermission::getMenuId)
                    .collect(Collectors.toList());
            dto.setMenuIds(menuIds);
        }
        
        return dto;
    }
    
    /**
     * RoleDTO转Role实体
     * 
     * @param dto 角色DTO
     * @return 角色实体
     */
    public Role toRole(RoleDTO dto) {
        if (dto == null) {
            return null;
        }
        
        Role role = new Role();
        role.setId(dto.getId());
        role.setRoleName(dto.getRoleName());
        role.setRoleCode(dto.getRoleCode());
        role.setRoleDescription(dto.getRoleDescription());
        role.setIsDefault(dto.getIsDefault());
        role.setCreateTime(dto.getCreateTime());
        role.setCreateBy(dto.getCreateBy());
        role.setUpdateTime(dto.getUpdateTime());
        role.setUpdateBy(dto.getUpdateBy());
        
        return role;
    }
    
    /**
     * Role实体列表转RoleDTO列表
     * 
     * @param roles 角色实体列表
     * @return 角色DTO列表
     */
    public List<RoleDTO> toRoleDTOList(List<Role> roles) {
        if (roles == null) {
            return null;
        }
        
        return roles.stream()
                .map(this::toRoleDTO)
                .collect(Collectors.toList());
    }
    
    // ========== User 转换 ==========
    
    /**
     * User实体转UserDTO
     * 
     * @param user 用户实体
     * @return 用户DTO
     */
    public UserDTO toUserDTO(User user) {
        if (user == null) {
            return null;
        }
        
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setUserCode(user.getUserCode());
        dto.setOrganization(user.getOrganization() != null ? user.getOrganization().getName() : null);
        dto.setStatus(user.getStatus() != null ? user.getStatus().getCode() : null);
        dto.setCreateTime(user.getCreateTime());
        dto.setCreateBy(user.getCreateBy());
        dto.setUpdateTime(user.getUpdateTime());
        dto.setUpdateBy(user.getUpdateBy());
        
        // 设置直接分配的角色ID列表
        dto.setRoleIds(user.getRoleIds());
        
        return dto;
    }
    
    /**
     * UserDTO转User实体
     * 
     * @param dto 用户DTO
     * @return 用户实体
     */
    public User toUser(UserDTO dto) {
        if (dto == null) {
            return null;
        }
        
        User user = new User();
        user.setId(dto.getId());
        user.setUsername(dto.getUsername());
        user.setUserCode(dto.getUserCode());
        user.setCreateTime(dto.getCreateTime());
        user.setCreateBy(dto.getCreateBy());
        user.setUpdateTime(dto.getUpdateTime());
        user.setUpdateBy(dto.getUpdateBy());
        
        return user;
    }
    
    /**
     * User实体列表转UserDTO列表
     * 
     * @param users 用户实体列表
     * @return 用户DTO列表
     */
    public List<UserDTO> toUserDTOList(List<User> users) {
        if (users == null) {
            return null;
        }
        
        return users.stream()
                .map(this::toUserDTO)
                .collect(Collectors.toList());
    }
    
    // ========== Menu 转换 ==========
    
    /**
     * Menu实体转MenuDTO
     * 
     * @param menu 菜单实体
     * @return 菜单DTO
     */
    public MenuDTO toMenuDTO(Menu menu) {
        if (menu == null) {
            return null;
        }
        
        MenuDTO dto = new MenuDTO();
        dto.setId(menu.getId());
        dto.setMenuName(menu.getMenuName());
        dto.setMenuCode(menu.getMenuCode());
        dto.setParentId(menu.getParentId());
        dto.setMenuPath(menu.getMenuPath());
        dto.setMenuIcon(menu.getMenuIcon());
        dto.setSortOrder(menu.getSortOrder());
        dto.setMenuLevel(menu.getMenuLevel());
        dto.setMenuType(menu.getMenuType() != null ? menu.getMenuType().getCode() : null);
        dto.setPermissionCode(menu.getPermissionCode());
        dto.setStatus(menu.getStatus() != null ? menu.getStatus().getCode() : null);
        dto.setCreateTime(menu.getCreateTime());
        dto.setUpdateTime(menu.getUpdateTime());
        
        // 转换子菜单
        if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
            dto.setChildren(toMenuDTOList(menu.getChildren()));
        }
        
        return dto;
    }
    
    /**
     * MenuDTO转Menu实体
     * 
     * @param dto 菜单DTO
     * @return 菜单实体
     */
    public Menu toMenu(MenuDTO dto) {
        if (dto == null) {
            return null;
        }
        
        Menu menu = new Menu();
        menu.setId(dto.getId());
        menu.setMenuName(dto.getMenuName());
        menu.setMenuCode(dto.getMenuCode());
        menu.setParentId(dto.getParentId());
        menu.setMenuPath(dto.getMenuPath());
        menu.setMenuIcon(dto.getMenuIcon());
        menu.setSortOrder(dto.getSortOrder());
        menu.setMenuLevel(dto.getMenuLevel());
        menu.setPermissionCode(dto.getPermissionCode());
        menu.setCreateTime(dto.getCreateTime());
        menu.setUpdateTime(dto.getUpdateTime());
        
        return menu;
    }
    
    /**
     * Menu实体列表转MenuDTO列表
     * 
     * @param menus 菜单实体列表
     * @return 菜单DTO列表
     */
    public List<MenuDTO> toMenuDTOList(List<Menu> menus) {
        if (menus == null) {
            return null;
        }
        
        return menus.stream()
                .map(this::toMenuDTO)
                .collect(Collectors.toList());
    }
    
    // ========== UserGroup 转换 ==========
    
    /**
     * UserGroup实体转UserGroupDTO
     * 
     * @param userGroup 用户组实体
     * @return 用户组DTO
     */
    public UserGroupDTO toUserGroupDTO(UserGroup userGroup) {
        if (userGroup == null) {
            return null;
        }
        
        UserGroupDTO dto = new UserGroupDTO();
        dto.setId(userGroup.getId());
        dto.setGroupName(userGroup.getGroupName());
        dto.setGroupCode(userGroup.getGroupCode());
        dto.setGroupDescription(userGroup.getGroupDescription());
        dto.setStatus(userGroup.getStatus() != null ? userGroup.getStatus().getCode() : null);
        dto.setCreateTime(userGroup.getCreateTime());
        dto.setCreateBy(userGroup.getCreateBy());
        dto.setUpdateTime(userGroup.getUpdateTime());
        dto.setUpdateBy(userGroup.getUpdateBy());
        
        // 设置成员和角色ID列表
        dto.setMemberIds(userGroup.getMemberIds());
        dto.setRoleIds(userGroup.getRoleIds());
        dto.setMemberCount(userGroup.getMemberIds().size());
        dto.setRoleCount(userGroup.getRoleIds().size());
        
        return dto;
    }
    
    /**
     * UserGroupDTO转UserGroup实体
     * 
     * @param dto 用户组DTO
     * @return 用户组实体
     */
    public UserGroup toUserGroup(UserGroupDTO dto) {
        if (dto == null) {
            return null;
        }
        
        UserGroup userGroup = new UserGroup();
        userGroup.setId(dto.getId());
        userGroup.setGroupName(dto.getGroupName());
        userGroup.setGroupCode(dto.getGroupCode());
        userGroup.setGroupDescription(dto.getGroupDescription());
        userGroup.setCreateTime(dto.getCreateTime());
        userGroup.setCreateBy(dto.getCreateBy());
        userGroup.setUpdateTime(dto.getUpdateTime());
        userGroup.setUpdateBy(dto.getUpdateBy());
        
        return userGroup;
    }
    
    /**
     * UserGroup实体列表转UserGroupDTO列表
     * 
     * @param userGroups 用户组实体列表
     * @return 用户组DTO列表
     */
    public List<UserGroupDTO> toUserGroupDTOList(List<UserGroup> userGroups) {
        if (userGroups == null) {
            return null;
        }
        
        return userGroups.stream()
                .map(this::toUserGroupDTO)
                .collect(Collectors.toList());
    }
}
