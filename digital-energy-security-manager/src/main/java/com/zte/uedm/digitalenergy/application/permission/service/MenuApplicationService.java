package com.zte.uedm.digitalenergy.application.permission.service;

import com.zte.uedm.digitalenergy.application.permission.dto.MenuDTO;
import com.zte.uedm.digitalenergy.application.permission.assembler.PermissionAssembler;
import com.zte.uedm.digitalenergy.common.exception.PermissionException;
import com.zte.uedm.digitalenergy.domain.permission.entity.Menu;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 菜单应用服务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class MenuApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(MenuApplicationService.class);
    
    @Autowired
    private MenuRepository menuRepository;
    
    @Autowired
    private PermissionAssembler permissionAssembler;
    
    /**
     * 查询菜单树
     * 
     * @return 菜单树
     */
    public List<MenuDTO> getMenuTree() {
        logger.debug("Getting menu tree");
        
        List<Menu> menuTree = menuRepository.buildEnabledMenuTree();
        return permissionAssembler.toMenuDTOList(menuTree);
    }
    
    /**
     * 根据用户ID查询用户菜单树
     * 
     * @param userId 用户ID
     * @return 用户菜单树
     */
    public List<MenuDTO> getUserMenuTree(Long userId) {
        logger.debug("Getting user menu tree for user: {}", userId);
        
        List<Menu> userMenuTree = menuRepository.buildUserMenuTree(userId);
        return permissionAssembler.toMenuDTOList(userMenuTree);
    }
    
    /**
     * 根据角色ID查询角色菜单列表
     * 
     * @param roleId 角色ID
     * @return 角色菜单列表
     */
    public List<MenuDTO> getRoleMenus(Long roleId) {
        logger.debug("Getting role menus for role: {}", roleId);
        
        List<Menu> roleMenus = menuRepository.findMenusByRoleId(roleId);
        return permissionAssembler.toMenuDTOList(roleMenus);
    }
    
    /**
     * 查询所有菜单
     * 
     * @return 菜单列表
     */
    public List<MenuDTO> getAllMenus() {
        logger.debug("Getting all menus");
        
        List<Menu> menus = menuRepository.findAllEnabled();
        return permissionAssembler.toMenuDTOList(menus);
    }
    
    /**
     * 根据ID查询菜单
     * 
     * @param id 菜单ID
     * @return 菜单信息
     */
    public MenuDTO getMenuById(Long id) {
        logger.debug("Getting menu by id: {}", id);
        
        Optional<Menu> optional = menuRepository.findById(id);
        if (optional.isEmpty()) {
            throw PermissionException.menuNotFound(id);
        }
        
        return permissionAssembler.toMenuDTO(optional.get());
    }
    
    /**
     * 根据菜单编码查询菜单
     * 
     * @param menuCode 菜单编码
     * @return 菜单信息
     */
    public MenuDTO getMenuByCode(String menuCode) {
        logger.debug("Getting menu by code: {}", menuCode);
        
        Optional<Menu> optional = menuRepository.findByMenuCode(menuCode);
        if (optional.isEmpty()) {
            throw PermissionException.menuNotFound(null);
        }
        
        return permissionAssembler.toMenuDTO(optional.get());
    }
}
