package com.zte.uedm.digitalenergy.interfaces.permission.controller;

import com.zte.uedm.digitalenergy.application.permission.dto.PageQueryDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.PageResultDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.RoleDTO;
import com.zte.uedm.digitalenergy.application.permission.service.RoleApplicationService;
import com.zte.uedm.digitalenergy.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "角色管理", description = "角色管理相关接口")
@RestController
@RequestMapping("/api/permission/roles")
@Validated
public class RoleController {
    
    private static final Logger logger = LoggerFactory.getLogger(RoleController.class);
    
    @Autowired
    private RoleApplicationService roleApplicationService;
    
    /**
     * 创建角色
     * 
     * @param roleDTO 角色信息
     * @param request HTTP请求
     * @return 创建的角色信息
     */
    @Operation(summary = "创建角色", description = "创建新的角色")
    @PostMapping
    public ApiResponse<RoleDTO> createRole(
            @Valid @RequestBody RoleDTO roleDTO,
            HttpServletRequest request) {
        
        logger.info("Creating role: {}", roleDTO.getRoleCode());
        
        String createBy = getCurrentUser(request);
        RoleDTO result = roleApplicationService.createRole(roleDTO, createBy);
        
        logger.info("Role created successfully with id: {}", result.getId());
        
        return ApiResponse.success("角色创建成功", result);
    }
    
    /**
     * 更新角色
     * 
     * @param id 角色ID
     * @param roleDTO 角色信息
     * @param request HTTP请求
     * @return 更新的角色信息
     */
    @Operation(summary = "更新角色", description = "更新指定角色信息")
    @PutMapping("/{id}")
    public ApiResponse<RoleDTO> updateRole(
            @Parameter(description = "角色ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody RoleDTO roleDTO,
            HttpServletRequest request) {
        
        logger.info("Updating role: {}", id);
        
        String updateBy = getCurrentUser(request);
        RoleDTO result = roleApplicationService.updateRole(id, roleDTO, updateBy);
        
        logger.info("Role updated successfully: {}", id);
        
        return ApiResponse.success("角色更新成功", result);
    }
    
    /**
     * 删除角色
     * 
     * @param id 角色ID
     * @return 删除结果
     */
    @Operation(summary = "删除角色", description = "删除指定角色")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteRole(
            @Parameter(description = "角色ID") @PathVariable @NotNull Long id) {
        
        logger.info("Deleting role: {}", id);
        
        roleApplicationService.deleteRole(id);
        
        logger.info("Role deleted successfully: {}", id);
        
        return ApiResponse.success("角色删除成功");
    }
    
    /**
     * 批量删除角色
     * 
     * @param ids 角色ID列表
     * @return 删除结果
     */
    @Operation(summary = "批量删除角色", description = "批量删除指定角色")
    @DeleteMapping("/batch")
    public ApiResponse<Void> deleteRoles(
            @Parameter(description = "角色ID列表") @RequestBody @NotEmpty List<Long> ids) {
        
        logger.info("Deleting roles: {}", ids);
        
        roleApplicationService.deleteRoles(ids);
        
        logger.info("Roles deleted successfully, count: {}", ids.size());
        
        return ApiResponse.success("角色批量删除成功");
    }
    
    /**
     * 根据ID查询角色
     * 
     * @param id 角色ID
     * @return 角色信息
     */
    @Operation(summary = "查询角色详情", description = "根据ID查询角色详细信息")
    @GetMapping("/{id}")
    public ApiResponse<RoleDTO> getRoleById(
            @Parameter(description = "角色ID") @PathVariable @NotNull Long id) {
        
        logger.debug("Getting role by id: {}", id);
        
        RoleDTO result = roleApplicationService.getRoleById(id);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色信息
     */
    @Operation(summary = "根据编码查询角色", description = "根据角色编码查询角色信息")
    @GetMapping("/code/{roleCode}")
    public ApiResponse<RoleDTO> getRoleByCode(
            @Parameter(description = "角色编码") @PathVariable @NotNull String roleCode) {
        
        logger.debug("Getting role by code: {}", roleCode);
        
        RoleDTO result = roleApplicationService.getRoleByCode(roleCode);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 分页查询角色列表
     * 
     * @param pageQuery 分页查询参数
     * @param roleName 角色名称（可选）
     * @param roleType 角色类型（可选）
     * @return 分页结果
     */
    @Operation(summary = "分页查询角色", description = "分页查询角色列表")
    @GetMapping("/page")
    public ApiResponse<PageResultDTO<RoleDTO>> getRolesByPage(
            @Valid PageQueryDTO pageQuery,
            @Parameter(description = "角色名称") @RequestParam(required = false) String roleName,
            @Parameter(description = "角色类型") @RequestParam(required = false) String roleType) {
        
        logger.debug("Getting roles by page: pageNum={}, pageSize={}, roleName={}, roleType={}", 
                    pageQuery.getPageNum(), pageQuery.getPageSize(), roleName, roleType);
        
        PageResultDTO<RoleDTO> result = roleApplicationService.getRolesByPage(pageQuery, roleName, roleType);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 查询所有角色
     * 
     * @return 角色列表
     */
    @Operation(summary = "查询所有角色", description = "查询系统中所有角色")
    @GetMapping("/all")
    public ApiResponse<List<RoleDTO>> getAllRoles() {
        
        logger.debug("Getting all roles");
        
        List<RoleDTO> result = roleApplicationService.getAllRoles();
        
        return ApiResponse.success(result);
    }
    
    /**
     * 根据角色类型查询角色列表
     * 
     * @param roleType 角色类型
     * @return 角色列表
     */
    @Operation(summary = "根据类型查询角色", description = "根据角色类型查询角色列表")
    @GetMapping("/type/{roleType}")
    public ApiResponse<List<RoleDTO>> getRolesByType(
            @Parameter(description = "角色类型") @PathVariable @NotNull String roleType) {
        
        logger.debug("Getting roles by type: {}", roleType);
        
        List<RoleDTO> result = roleApplicationService.getRolesByType(roleType);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 获取当前用户
     * 
     * @param request HTTP请求
     * @return 当前用户
     */
    private String getCurrentUser(HttpServletRequest request) {
        // 这里应该从Security Context或JWT Token中获取当前用户
        // 暂时返回固定值，实际项目中需要实现
        String user = request.getHeader("X-User-Code");
        return user != null ? user : "system";
    }
}
