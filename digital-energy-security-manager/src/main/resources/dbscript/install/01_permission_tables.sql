-- 权限管理系统数据库表结构
-- 支持幂等性执行

-- 1. 菜单表
CREATE TABLE IF NOT EXISTS t_menu (
    id BIGSERIAL PRIMARY KEY,
    menu_name VARCHAR(100) NOT NULL COMMENT '菜单名称',
    menu_code VARCHAR(100) NOT NULL UNIQUE COMMENT '菜单编码',
    parent_id BIGINT DEFAULT 0 COMMENT '父菜单ID，0表示根菜单',
    menu_path VARCHAR(200) COMMENT '菜单路径',
    menu_icon VARCHAR(100) COMMENT '菜单图标',
    sort_order INTEGER DEFAULT 0 COMMENT '排序号',
    menu_level INTEGER DEFAULT 1 COMMENT '菜单层级',
    status INTEGER DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建菜单表索引
CREATE INDEX IF NOT EXISTS idx_menu_parent_id ON t_menu(parent_id);
CREATE INDEX IF NOT EXISTS idx_menu_code ON t_menu(menu_code);
CREATE INDEX IF NOT EXISTS idx_menu_status ON t_menu(status);

-- 2. 角色表
CREATE TABLE IF NOT EXISTS t_role (
    id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(20) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    role_description VARCHAR(200) COMMENT '角色描述',
    role_type VARCHAR(20) DEFAULT 'CUSTOM' COMMENT '角色类型：DEFAULT-默认角色，CUSTOM-自定义角色',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认角色',
    status INTEGER DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(50) COMMENT '更新人'
);

-- 创建角色表索引
CREATE INDEX IF NOT EXISTS idx_role_code ON t_role(role_code);
CREATE INDEX IF NOT EXISTS idx_role_type ON t_role(role_type);
CREATE INDEX IF NOT EXISTS idx_role_status ON t_role(status);

-- 3. 用户表
CREATE TABLE IF NOT EXISTS t_user (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    user_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户工号',
    organization VARCHAR(200) COMMENT '组织机构',
    status INTEGER DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(50) COMMENT '更新人'
);

-- 创建用户表索引
CREATE INDEX IF NOT EXISTS idx_user_code ON t_user(user_code);
CREATE INDEX IF NOT EXISTS idx_user_status ON t_user(status);

-- 4. 用户组表
CREATE TABLE IF NOT EXISTS t_user_group (
    id BIGSERIAL PRIMARY KEY,
    group_name VARCHAR(50) NOT NULL COMMENT '用户组名称',
    group_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户组编码',
    group_description VARCHAR(200) COMMENT '用户组描述',
    status INTEGER DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(50) COMMENT '更新人'
);

-- 创建用户组表索引
CREATE INDEX IF NOT EXISTS idx_user_group_code ON t_user_group(group_code);
CREATE INDEX IF NOT EXISTS idx_user_group_status ON t_user_group(status);

-- 5. 角色菜单关联表
CREATE TABLE IF NOT EXISTS t_role_menu (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    menu_id BIGINT NOT NULL COMMENT '菜单ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    CONSTRAINT fk_role_menu_role FOREIGN KEY (role_id) REFERENCES t_role(id) ON DELETE CASCADE,
    CONSTRAINT fk_role_menu_menu FOREIGN KEY (menu_id) REFERENCES t_menu(id) ON DELETE CASCADE,
    CONSTRAINT uk_role_menu UNIQUE (role_id, menu_id)
);

-- 创建角色菜单关联表索引
CREATE INDEX IF NOT EXISTS idx_role_menu_role_id ON t_role_menu(role_id);
CREATE INDEX IF NOT EXISTS idx_role_menu_menu_id ON t_role_menu(menu_id);

-- 6. 用户角色关联表
CREATE TABLE IF NOT EXISTS t_user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    CONSTRAINT fk_user_role_user FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_role_role FOREIGN KEY (role_id) REFERENCES t_role(id) ON DELETE CASCADE,
    CONSTRAINT uk_user_role UNIQUE (user_id, role_id)
);

-- 创建用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_role_user_id ON t_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_role_id ON t_user_role(role_id);

-- 7. 用户组角色关联表
CREATE TABLE IF NOT EXISTS t_user_group_role (
    id BIGSERIAL PRIMARY KEY,
    user_group_id BIGINT NOT NULL COMMENT '用户组ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    CONSTRAINT fk_user_group_role_group FOREIGN KEY (user_group_id) REFERENCES t_user_group(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_group_role_role FOREIGN KEY (role_id) REFERENCES t_role(id) ON DELETE CASCADE,
    CONSTRAINT uk_user_group_role UNIQUE (user_group_id, role_id)
);

-- 创建用户组角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_group_role_group_id ON t_user_group_role(user_group_id);
CREATE INDEX IF NOT EXISTS idx_user_group_role_role_id ON t_user_group_role(role_id);

-- 8. 用户组成员关联表
CREATE TABLE IF NOT EXISTS t_user_group_member (
    id BIGSERIAL PRIMARY KEY,
    user_group_id BIGINT NOT NULL COMMENT '用户组ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    CONSTRAINT fk_user_group_member_group FOREIGN KEY (user_group_id) REFERENCES t_user_group(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_group_member_user FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE CASCADE,
    CONSTRAINT uk_user_group_member UNIQUE (user_group_id, user_id)
);

-- 创建用户组成员关联表索引
CREATE INDEX IF NOT EXISTS idx_user_group_member_group_id ON t_user_group_member(user_group_id);
CREATE INDEX IF NOT EXISTS idx_user_group_member_user_id ON t_user_group_member(user_id);

-- 9. 权限操作审计日志表
CREATE TABLE IF NOT EXISTS t_permission_audit_log (
    id BIGSERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：CREATE,UPDATE,DELETE',
    operator VARCHAR(50) NOT NULL COMMENT '操作人',
    target_type VARCHAR(50) NOT NULL COMMENT '目标类型：ROLE,USER,USER_GROUP',
    target_id BIGINT COMMENT '目标ID',
    old_value TEXT COMMENT '操作前值',
    new_value TEXT COMMENT '操作后值',
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理'
);

-- 创建审计日志表索引
CREATE INDEX IF NOT EXISTS idx_audit_log_operator ON t_permission_audit_log(operator);
CREATE INDEX IF NOT EXISTS idx_audit_log_operation_time ON t_permission_audit_log(operation_time);
CREATE INDEX IF NOT EXISTS idx_audit_log_target ON t_permission_audit_log(target_type, target_id);
